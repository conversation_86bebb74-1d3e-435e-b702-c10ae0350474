import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { TransportService } from './transport.service';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { AuthUser } from '../auth/decorators/auth-user.decorator';
import { User } from '@clerk/backend';
import { GuardUser } from '../../decorators/guard-user.decorator';
import { CreateTelegramTransportUserDto, CreateTransportUserDto, CreateWidgetTransportUserDto } from './dtos/create-transport-user.dto';
import { TransportUserEntity } from './entities/transport-user.entity';
import { TransportUserDto } from './dtos/transport-user.dto';
import { ApiGetManyDto } from '../../decorators';
import { replaceGetManyFilter } from '../../common/utils';

@Controller('user-transports')
@ApiTags('User Transport')
export class TransportController {
  constructor(private readonly transportService: TransportService) { }

  @Post('pure-api')
  @GuardUser()
  @ApiOperation({ summary: 'Create a Pure API Transport User' })
  async createPureApiTransportUser(@AuthUser() { externalId }: User) {
    const apiKey = await this.transportService.generateAPIKey();
    const pureApiTransport = await this.transportService.createPureApiTransportUser(externalId!, apiKey);
    const pureApiTransportDto = pureApiTransport.toDto();

    return {
      ...pureApiTransportDto,
      apiKey,
    };
  }

  @Post('telegram')
  @GuardUser()
  @ApiOperation({ summary: 'Create a Telegram Transport User' })
  async createTelegramTransportUser(@AuthUser() { externalId }: User, @Body() args: CreateTelegramTransportUserDto) {
    if (!args.token) {
      throw new BadRequestException('telegram bot token not exist');
    }
    const telegramTransport = await this.transportService.createTelegramTransportUser(externalId!, args.token);
    return telegramTransport.toDto();
  }

  @Post('widget')
  @GuardUser()
  @ApiOperation({ summary: 'Create a Telegram Transport User' })
  async createWidgetTransportUser(@AuthUser() { externalId }: User, @Body() args: CreateWidgetTransportUserDto) {
    const apiKey = await this.transportService.generatePublishableKey();
    args.publishableKey = apiKey
    const widgetTransport = await this.transportService.createWidgetTransportUser(externalId!, args);
    return widgetTransport.toDto();
  }

  @Get()
  @GuardUser()
  @ApiOperation({ summary: 'Get all Transport Users for Authenticated User' })
  @ApiGetManyDto({ description: 'List of user transport configurations', type: TransportUserDto })
  async getManyAuthUserTransportUsers(
    @AuthUser() { externalId }: User,
    @Query() args: GetManyDto<TransportUserEntity>,
  ) {
    args.filter = replaceGetManyFilter(args.filter, 'userId', `userId:eq:${externalId!}`)
    const transports = await this.transportService.getManyUserTransport(args, {
      toDto: true,
    });

    return transports
  }

  @Get(':id')
  @GuardUser()
  @ApiOperation({ summary: 'Get one Transport User by ID for Authenticated User' })
  @ApiResponse({ status: 200, description: 'Single transport user', type: TransportUserDto })
  async getOneAuthUserTransportUser(@AuthUser() { externalId }: User, @Param('id') id: string) {
    const { data } = await this.transportService.getManyUserTransport(
      { filter: [`userId:eq:${externalId}`, `id:eq:${id}`], page: 1, limit: 1 },
      { toDto: true },
    );

    if (data.length == 0) {
      throw new NotFoundException('knowledge base not found');
    }

    return data[0];
  }

  @Delete(':id')
  @GuardUser()
  @ApiOperation({ summary: 'Delete a Transport User by ID' })
  @ApiResponse({ status: 204, description: 'Transport user deleted' })
  async deleteTransportUser(@AuthUser() { externalId }: User, @Param('id') id: string) {
    const { data } = await this.transportService.getManyUserTransport(
      {
        filter: [`userId:eq:${externalId!}`, `id:eq:${id}`],
      },
      {
        toDto: true,
      },
    );

    if (data.length == 0) {
      throw new NotFoundException('user transport not found');
    }

    await this.transportService.revoke(data[0]!.id);
  }
}
