import { <PERSON>umField, UUIDField } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { FeedbackEntity } from '../entities/feedback.entity';
import { MessageDto } from '../../messages/dtos/messages.dto';
import { FeedbackType } from '../enums/feedback-type.enum';

export class FeedbackDto extends AbstractDto {
  @UUIDField()
  messageId: string;

  @EnumField(() => FeedbackType)
  feedbackType: FeedbackType | keyof typeof FeedbackType;

  message?: MessageDto;

  constructor(e: FeedbackEntity) {
    super(e);

    this.messageId = e.messageId;
    this.feedbackType = e.feedbackType;
    this.message = e.message?.toDto()
  }
}
