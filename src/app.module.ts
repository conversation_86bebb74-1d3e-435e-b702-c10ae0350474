import './boilerplate.polyfill';

import type { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { Module, RequestMethod } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';

import { JsonBodyMiddleware } from './middlewares/json-body.middleware';
import { RawBodyMiddleware } from './middlewares/raw-body.middleware';
import { SharedModule } from './shared/shared.module';
import { UserModule } from './modules/user/user.module';
import { PromptModule } from './modules/prompt/prompt.module';
import { ToolsModule } from './modules/tools/tools.module';
import { SessionsModule } from './modules/sessions/sessions.module';
import { MessagesModule } from './modules/messages/messages.module';
import { TransportModule } from './modules/transports/transport.module';
import { KnowledgeBaseModule } from './modules/knowledge-base/knowledge-base.module';
import { LlmModule } from './modules/llm/llm.module';
import AuthModule from './modules/auth/auth.module';
import { BillingModule } from './modules/billing/billing.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { NotificationModule } from './modules/mail/notification.module';
import { InitModule } from './modules/init/init.module';
import HealthModule from './modules/health/health.module';
import { FeedbackModule } from './modules/feedback/feedback.module';

@Module({
  imports: [
    SharedModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60,
          limit: 10,
        },
      ],
    }),
    EventEmitterModule.forRoot(),
    UserModule,
    PromptModule,
    ToolsModule,
    SessionsModule,
    MessagesModule,
    TransportModule,
    LlmModule,
    AuthModule,
    KnowledgeBaseModule,
    BillingModule,
    NotificationModule,
    InitModule,
    HealthModule,
    FeedbackModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule implements NestModule {
  public configure(consumer: MiddlewareConsumer): void {
    consumer
      .apply(RawBodyMiddleware)
      .forRoutes({
        path: '/auth/webhook',
        method: RequestMethod.POST,
      })
      .apply(JsonBodyMiddleware)
      .forRoutes('*');
  }
}
