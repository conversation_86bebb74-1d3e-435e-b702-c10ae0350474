import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { FeedbackRepository } from './feedback.repository';
import { FeedbackEntity } from './entities/feedback.entity';
import { MessageService } from '../messages/message.service';
import { FeedbackType } from './enums/feedback-type.enum';

@Injectable()
export class FeedbackService {
  constructor(
    private readonly repository: FeedbackRepository,
    private readonly messageService: MessageService,
  ) { }

  async createFeedback(
    feedbackType: FeedbackType,
    messageId: string,
  ): Promise<FeedbackEntity> {
    const { data: messages } = await this.messageService.getMany({
      filter: [`id:eq:${messageId}`]
    }, {});
    if (messages.length == 0) {
      throw new NotFoundException('Message not found');
    }

    const existingFeedback = await this.findByMessageId(messageId);
    if (existingFeedback) {
      existingFeedback.assign({
        feedbackType: feedbackType,
        messageId: messageId
      })

      return this.repository.save(existingFeedback);
    }

    const feedback = this.repository.create({
      messageId,
      feedbackType,
    });

    return this.repository.save(feedback);
  }

  async getFeedbackById(id: string): Promise<FeedbackEntity> {
    const feedback = await this.repository.findById(id);
    if (!feedback) {
      throw new NotFoundException('Feedback not found');
    }
    return feedback;
  }

  async findByMessageId(messageId: string) {
    return this.repository.findOne({
      where: { messageId },
      relations: ['message'],
    });
  }
}
