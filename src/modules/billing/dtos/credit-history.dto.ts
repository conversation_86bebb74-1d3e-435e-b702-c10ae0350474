import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, Enum<PERSON>ield } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { CreditHistoryEntity } from '../entities/credit-history.entity';
import { CreditHistoryReasonType } from '../enums/credit-history-reason-type.enum';

export class CreditHistoryDto extends AbstractDto {
  @UUIDField()
  userId: string;

  @UUIDField({ nullable: true })
  sessionId?: string | null;

  @NumberField()
  amount: number;

  @StringField({ nullable: true })
  description?: string | null;

  @EnumField(() => CreditHistoryReasonType)
  reason: CreditHistoryReasonType | keyof typeof CreditHistoryReasonType;

  constructor(entity: CreditHistoryEntity) {
    super(entity);
    this.userId = entity.userId;
    this.sessionId = entity.sessionId ?? null;
    this.amount = entity.amount;
    this.description = entity.description ?? null;
    this.reason = entity.reason;
  }
}

