import { join } from 'node:path';

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { isNil } from 'lodash';
import { SnakeNamingStrategy } from '../../snake-naming.strategy';

@Injectable()
export class ApiConfigService {
  constructor(private configService: ConfigService) { }

  get isDevelopment(): boolean {
    return this.nodeEnv === 'development';
  }

  get isProduction(): boolean {
    return this.nodeEnv === 'production';
  }

  get isTest(): boolean {
    return this.nodeEnv === 'test';
  }

  private getNumber(key: string): number {
    const value = this.get(key);

    try {
      return Number(value);
    } catch {
      throw new Error(key + ' environment variable is not a number');
    }
  }

  private getBoolean(key: string): boolean {
    const value = this.get(key);

    try {
      return Boolean(JSON.parse(value));
    } catch {
      throw new Error(key + ' env var is not a boolean');
    }
  }

  private getString(key: string): string {
    const value = this.get(key);

    return value.replaceAll('\\n', '\n');
  }

  get nodeEnv(): string {
    return this.getString('NODE_ENV');
  }

  get documentationEnabled(): boolean {
    return this.getBoolean('ENABLE_DOCUMENTATION');
  }

  get authConfig() {
    return {
      secret: this.getString('JWT_SECRET'),
      jwtExpirationTime: this.getString('JWT_EXPIRATION_TIME'),
    };
  }

  get appConfig() {
    return {
      port: this.getString('PORT'),
    };
  }

  get dbConfig(): TypeOrmModuleOptions {
    const entities = [__dirname + '/../../{shared,modules}/**/entities/*.entity{.ts,.js}'];
    const migrations = [__dirname + '/../../databases/migrations/*{.ts,.js}'];

    return {
      dropSchema: this.isTest,
      type: 'postgres',
      url: this.get('DB_URL'),
      logging: this.getBoolean('ENABLE_ORM_LOGS'),
      connectTimeoutMS: this.getNumber('DB_CONNECTION_TIMEOUT_IN_MS'),
      entities,
      logger: 'advanced-console',
      namingStrategy: new SnakeNamingStrategy(),
      migrationsRun: true,
      migrations,
    };
  }

  get clerkConfig() {
    return {
      publishableKey: this.getString('CLERK_PUBLISHABLE_KEY'),
      secretKey: this.getString('CLERK_SECRET_KEY'),
      webhookSecret: this.getString('CLERK_WEBHOOK_SECRET'),
    };
  }

  get qdrantConfig() {
    return {
      url: this.getString('QDRANT_URL'),
      vectorSize: this.getNumber('QDRANT_VECTOR_SIZE'),
      distance: this.getString('QDRANT_DISTANCE') as 'Cosine' | 'Euclid' | 'Dot' | 'Manhattan',
      apiKey: this.getString('QDRANT_API_KEY'),
    };
  }

  get openAiConfig() {
    return {
      apiKey: this.getString('OPENAI_API_KEY'),
      url: this.getString('OPENAI_URL'),
      model: this.getString('OPENAI_MODEL'),
    };
  }

  get s3Config() {
    return {
      endpoint: this.getString('S3_ENDPOINT'),
      accessKeyId: this.getString('S3_ACCESS_KEY_ID'),
      secretAccessKey: this.getString('S3_SECRET_ACCESS_KEY'),
      bucket: this.getString('S3_BUCKET'),
    };
  }

  get rdeConfig() {
    return {
      url: this.getString('RDE_URL'),
      apiKey: this.getString('RDE_API_KEY'),
    };
  }

  get mailConfig() {
    return {
      host: this.getString('SMTP_HOST'),
      port: this.getNumber('SMTP_PORT'),
      secure: this.getBoolean('SMTP_SECURE'),
      user: this.getString('SMTP_USER'),
      pass: this.getString('SMTP_PASSWORD'),
      senderName: this.getString('SMTP_SENDER_NAME'),
    };
  }

  get transportConfig() {
    return {
      telegram: {
        webhookUrl: this.getString('TRANSPORT_TELEGRAM_WEBHOOK_URL'),
      },
    };
  }

  get langfuseConfig() {
    return {
      publicKey: this.getString("LANGFUSE_PUBLIC_KEY"),
      secretKey: this.getString("LANGFUSE_SECRET_KEY"),
      baseUrl: this.getString("LANGFUSE_BASEURL")
    };
  }


  get tryspeedConfig() {
    return {
      webhookSecret: this.getString("TRYSPEED_WEBHOOK_SECRET"),
      apiKey: this.getString("TRYSPEED_API_KEY"),
      failedPaymentUrl: this.getString("TRYSPEED_FAILED_PAYMNET_URL"),
      successPaymentUrl: this.getString("TRYSPEED_SUCCESSFUL_PAYMNET_URL"),
      apiUrl: this.getString("TRYSPEED_API_URL")
    };
  }

  private get(key: string): string {
    const value = this.configService.get<string>(key);

    if (isNil(value)) {
      throw new Error(key + ' environment variable does not set');
    }

    return value;
  }
}
