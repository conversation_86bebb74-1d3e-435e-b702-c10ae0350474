import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index, OneToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { FeedbackDto } from '../dtos/feedback.dto';
import { MessageEntity } from '../../messages/entities/messages.entity';
import { FeedbackType } from '../enums/feedback-type.enum';

@Entity('feedback')
@Index(['messageId'])
export class FeedbackEntity extends AbstractEntity<FeedbackDto> {
  dtoClass = FeedbackDto;

  @OneToOne(() => MessageEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'message_id' })
  message?: MessageEntity;

  @Column()
  messageId: string;

  @Column({ type: 'enum', enum: FeedbackType })
  feedbackType: FeedbackType | keyof typeof FeedbackType;

  constructor(item?: Partial<FeedbackEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<FeedbackEntity>): void {
    super.assign(item);

    this.messageId = item.messageId ?? this.messageId;
    this.feedbackType = item.feedbackType ?? this.feedbackType;
  }
}
