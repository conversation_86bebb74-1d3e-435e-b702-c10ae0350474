import { AbstractEntity } from '../../../common/abstract.entity';
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { CreditHistoryDto } from '../dtos/credit-history.dto';
import { UserEntity } from '../../user/entities/user.entity';
import { SessionsEntity } from '../../sessions/entities/sessions.entity';
import { CreditHistoryReasonType } from '../enums/credit-history-reason-type.enum';



@Entity('credit_history')
export class CreditHistoryEntity extends AbstractEntity<CreditHistoryDto> {
  dtoClass = CreditHistoryDto;

  @ManyToOne(() => UserEntity, (user) => user.id)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column()
  @Index()
  userId: string;

  @ManyToOne(() => SessionsEntity, (s) => s.id, { nullable: true })
  @JoinColumn({ name: 'session_id' })
  session?: SessionsEntity | null;

  @Column({ nullable: true })
  @Index()
  sessionId?: string | null;

  @Column({ type: 'integer', default: 0 })
  amount: number;

  // Human readable description e.g. "LLM tokens for session", "KB ingestion"
  @Column({ type: 'varchar', nullable: true })
  description?: string | null;

  @Column({ type: 'enum', enum: CreditHistoryReasonType })
  reason: CreditHistoryReasonType;

  constructor(item?: Partial<CreditHistoryEntity>) {
    super();
    if (!item) return;
    this.assign(item);
  }

  assign(item: Partial<CreditHistoryEntity>) {
    super.assign(item);
    this.userId = item.userId ?? this.userId;
    this.sessionId = item.sessionId ?? this.sessionId;
    this.amount = item.amount ?? this.amount;
    this.description = item.description ?? this.description;
    this.reason = item.reason ?? this.reason;
  }
}

