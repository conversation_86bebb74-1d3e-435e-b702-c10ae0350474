name: Create and publish a rokovo-core image

on:
  push:
    tags:
      - "v*"

env:
  REGISTRY: registry.rokovo.io
  IMAGE_NAME: rokovo/rokovo-core

jobs:
  build-and-push-image:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Extract version from Git tag
        id: version
        run: |
          RAW_TAG="${GITHUB_REF##*/}"
          VERSION="${RAW_TAG#rokovo-core@}"
          echo "VERSION=$VERSION" >> "$GITHUB_OUTPUT"

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.ROKOVO_REGISTRY_USERNAME }}
          password: ${{ secrets.ROKOVO_REGISTRY_PASSWORD }}

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and push Docker image
        id: push
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.VERSION }}
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
          build-args: |
            LABEL_MAINTAINER=${{ github.actor }}
            LABEL_VERSION=${{ steps.version.outputs.VERSION }}
            LABEL_REPO=${{ github.repository }}
            LABEL_COMMIT=${{ github.sha }}
