import { Injectable } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { CreditHistoryEntity } from './entities/credit-history.entity';
import { CreditHistoryReasonType } from './enums/credit-history-reason-type.enum';
import { CreditHistoryRepository } from './credit-history.repository';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { GetManyDtoOptions } from '../../common/abstract.repository';

@Injectable()
export class CreditHistoryService {
  constructor(private readonly repo: CreditHistoryRepository) {}

  async getMany<T = CreditHistoryEntity>(q: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
    return this.repo.getMany(q, opt);
  }

  @Transactional()
  async create(args: {
    userId: string;
    amount: number;
    reason: CreditHistoryReasonType;
    sessionId?: string;
    description?: string;
  }) {
    const history = this.repo.create({
      userId: args.userId,
      amount: args.amount,
      reason: args.reason,
      sessionId: args.sessionId,
      description: args.description,
    });
    return this.repo.save(history);
  }

  @Transactional()
  async incrementSessionAmount(userId: string, sessionId: string, amount: number, description?: string) {
    // Find existing record for this session, otherwise create
    let record = await this.repo.findOne({ where: { userId, sessionId } });
    if (!record) {
      record = this.repo.create({ userId, sessionId, amount: 0, reason: CreditHistoryReasonType.SESSION });
    }
    record.amount += amount;
    if (description) record.description = description;
    return this.repo.save(record);
  }
}

