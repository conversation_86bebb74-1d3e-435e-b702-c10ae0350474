import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InvoiceEntity } from './entities/invoice.entity';
import { InvoiceStatus } from './enums/invoice-status.enum';
import { PaymentProvider } from './enums/payment-provider.enum';
import { CreditEntity } from './entities/credit.entity';
import { BillingService } from './billing.service';
import { InvoiceRepository } from './invoice.repository';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Transactional } from 'typeorm-transactional';
import { UserService } from '../user/user.service';
import { v4 as uuidv4 } from 'uuid';
import {
  InvoicePaidEvent,
  NOTIFICATION_EVENTS
} from '../mail/interfaces/notification-events.interface';

@Injectable()
export class InvoiceService {
  constructor(
    private readonly invoiceRepo: InvoiceRepository,
    private readonly billingService: BillingService,
    private readonly eventEmitter: EventEmitter2,
    private readonly userService: UserService,
  ) {}

  /**
   * Creates a new invoice and associates it with a credit account.
   */
  @Transactional()
  async createInvoice(input: {
    userId: string;
    amount: number;
    paymentProvider: PaymentProvider;
    paymentMethod?: string;
    description?: string;
  }): Promise<InvoiceEntity> {
    const credit = await this.billingService.getOrCreateUserCredit(input.userId);

    const invoice = this.invoiceRepo.create({
      credit,
      amount: input.amount,
      issuedAt: new Date(),
      status: InvoiceStatus.PENDING,
      paymentProviderId: input.paymentProvider,
      paymentMethod: input.paymentMethod,
      description: input.description,
    });

    return await this.invoiceRepo.save(invoice);
  }

  /**
   * Marks an invoice as paid and updates user's credit.
   */
  @Transactional()
  async markInvoiceAsPaid(invoiceId: string): Promise<InvoiceEntity> {
    const invoice = await this.invoiceRepo.findOne({
      where: { id: invoiceId },
      relations: ['credit'],
    });

    if (!invoice) {
      throw new NotFoundException('Invoice not found');
    }

    if (invoice.status === InvoiceStatus.PAID) {
      throw new BadRequestException('Invoice is already marked as paid');
    }

    invoice.status = InvoiceStatus.PAID;
    invoice.paidAt = new Date();

    await this.billingService.addCredit(invoice.credit.userId, invoice.amount);

    const updatedInvoice = await this.invoiceRepo.save(invoice);

    // Get user information for the event
    const user = await this.userService.findById(invoice.credit.userId);

    // Emit properly structured invoice paid event
    const event: InvoicePaidEvent = {
      timestamp: new Date(),
      eventId: uuidv4(),
      invoice: updatedInvoice,
      user,
      creditsPurchased: invoice.amount,
    };

    this.eventEmitter.emit(NOTIFICATION_EVENTS.INVOICE_PAID, event);

    return updatedInvoice;
  }

  /**
   * Gets all invoices for a given user.
   */
  async getUserInvoices(userId: string): Promise<InvoiceEntity[]> {
    const credit = await this.billingService.getOrCreateUserCredit(userId);

    return await this.invoiceRepo.findAll({
      where: { credit: { id: credit.id } },
      order: { issuedAt: 'DESC' },
    });
  }

  /**
   * Cancels an unpaid invoice.
   */
  @Transactional()
  async cancelInvoice(invoiceId: string): Promise<InvoiceEntity> {
    const invoice = await this.invoiceRepo.findOne({ where: { id: invoiceId } });

    if (!invoice) {
      throw new NotFoundException('Invoice not found');
    }

    if (invoice.status !== InvoiceStatus.PENDING) {
      throw new BadRequestException('Only pending invoices can be cancelled');
    }

    invoice.status = InvoiceStatus.CANCELLED;
    return await this.invoiceRepo.save(invoice);
  }

  /**
   * Retrieves a single invoice by ID.
   */
  async getInvoiceById(invoiceId: string): Promise<InvoiceEntity> {
    const invoice = await this.invoiceRepo.findOrFail({ where: { id: invoiceId } });

    return invoice;
  }
}
