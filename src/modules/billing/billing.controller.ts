import { Body, Controller, Get, Post, Query, Headers, UnauthorizedException } from '@nestjs/common';
import { BillingService } from './billing.service';
import { ApiTags, ApiOperation, ApiResponse, ApiExtraModels } from '@nestjs/swagger';
import { AuthUser } from '../auth/decorators/auth-user.decorator';
import { User } from '@clerk/backend';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { CreditEntity } from './entities/credit.entity';
import { GuardUser } from '../../decorators/guard-user.decorator';
import { BillingDto } from './dtos/credit.dto';
import { ApiGetManyDto } from '../../decorators';
import { replaceGetManyFilter } from '../../common/utils';
import { CreditGenerateCheckoutSessionDto } from './dtos/credit-generate-checkout-session.dto';
import { ApiConfigService } from 'src/shared/services/api-config.service';
import { InvoiceService } from './invoice.service';
import { PaymentProvider } from './enums/payment-provider.enum';

@Controller('billing')
@ApiTags('Billing')
export class BillingController {
  constructor(
    private readonly service: BillingService,
    private readonly apiConfig: ApiConfigService,
    private readonly invoiceService: InvoiceService,
  ) { }

  @Get('/credit')
  @ApiOperation({
    summary: 'Get credit for authenticated user',
    description: 'Returns the current credit balance of the logged-in user.',
  })
  @ApiResponse({ status: 200, description: 'Successfully retrieved credit', type: Number })
  @GuardUser()
  async getCredit(@AuthUser() user: User): Promise<number> {
    const available = await this.service.getUserCredit(user.externalId!);
    return available;
  }

  @Get('/price')
  @ApiOperation({
    summary: 'Get get price of amount of a credit in USD',
    description: '',
  })
  @GuardUser()
  async getPrice(@Body() amount: number): Promise<number> {
    return amount * this.service.getUDSPerToken()
  }

  @Get()
  @ApiOperation({
    summary: 'List user Billings',
    description: 'Returns paginated list of Billing activity for the authenticated user.',
  })
  @ApiGetManyDto({ type: BillingDto, description: 'Paginated list of Billing records' })
  @GuardUser()
  async getMany(@AuthUser() user: User, @Query() q: GetManyDto<CreditEntity>) {
    q.filter = replaceGetManyFilter(q.filter, 'userId', `userId:eq:${user.externalId!}`);

    const transports = await this.service.getMany(q, {
      toDto: true,
    });
    return transports;
  }

  @Post('tryspeed/checkout-session')
  @GuardUser()
  async generateCheckoutSession(@Body() args: CreditGenerateCheckoutSessionDto) {
    const invoice = await this.invoiceService.createInvoice({
      amount: this.service.getUDSPerToken() * args.creditAmount,
      paymentProvider: PaymentProvider.TRYSPEED,
      userId: args.userId,
    })
    return await this.service.generateTrySpeedCheckoutSession(
      args.creditAmount, args.userId, invoice.id
    );
  }

  @Post('tryspeed/webhook')
  async webhook(
    @Headers('webhook-signature') signature: string,
    @Headers('webhook-timestamp') timestamp: string,
    @Headers('webhook-id') webhookId: string,
    @Body() body: any,
  ) {
    const secret = this.apiConfig.tryspeedConfig.webhookSecret;

    const isValid = this.service.verifySignature(secret,
      signature, webhookId, timestamp, body);

    if (!isValid) {
      throw new UnauthorizedException('Invalid webhook signature');
    }

    const bodyString = body.toString('utf-8');
    const parsedBody = JSON.parse(bodyString);

    if (parsedBody.event_type === 'checkout_session.paid') {
      const object = parsedBody.data.object;
      const metadata = object.metadata;
      try {
        await this.invoiceService.markInvoiceAsPaid(metadata.invoiceId as string)
        await this.service.addCredit(metadata.userId as string, metadata.creditAmount as number)
        return { success: true }
      } catch {
        return { success: false }
      }
    }

    return { success: true }
  }
}
