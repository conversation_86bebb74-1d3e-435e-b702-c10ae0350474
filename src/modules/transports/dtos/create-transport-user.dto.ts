import { PickType } from '@nestjs/swagger';
import { TransportUserDto } from './transport-user.dto';
import { StringField } from '../../../decorators';
import { WidgetConfig } from './transport-setting.dto';

export class CreateTransportUserDto extends PickType(TransportUserDto, ['setting'] as const) {}
export class CreateWidgetTransportUserDto extends PickType(WidgetConfig, ['primaryColor', 'publishableKey', 'secondaryColor', 'style'] as const) {}

export class CreateTelegramTransportUserDto {
  @StringField()
  token: string;
}
