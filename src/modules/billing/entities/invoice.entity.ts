import { AbstractEntity } from '../../../common/abstract.entity';
import { Column, Entity, ManyToOne, JoinColumn } from 'typeorm';
import { InvoiceStatus } from '../enums/invoice-status.enum';
import { PaymentProvider } from '../enums/payment-provider.enum';
import { InvoiceDto } from '../dtos/invoice.dto';
import { CreditEntity } from './credit.entity';
import { Currency } from '../enums/currency.dto';

@Entity('invoices')
export class InvoiceEntity extends AbstractEntity<InvoiceDto> {
  dtoClass = InvoiceDto;

  @ManyToOne(() => CreditEntity, (s) => s.invoices)
  @JoinColumn({ name: 'credit_id' })
  credit: CreditEntity;

  @Column()
  creditId: string;

  @Column()
  amount: number;

  @Column({ type: 'enum', enum: Currency })
  currency: Currency;

  @Column({ type: 'timestamp' })
  issuedAt: Date;

  @Column({ type: 'timestamp' })
  paidAt: Date;

  @Column({ type: 'enum', enum: InvoiceStatus, default: InvoiceStatus.PENDING })
  status: InvoiceStatus;

  @Column({ type: 'enum', enum: PaymentProvider })
  paymentProviderId: PaymentProvider;

  @Column({ nullable: true })
  paymentMethod: string; // e.g., credit card, PayPal

  @Column({ nullable: true })
  description: string;

  constructor(init?: Partial<InvoiceEntity>) {
    super();
    if (init) {
      this.assign(init);
    }
  }

  assign(partial: Partial<InvoiceEntity>) {
    Object.assign(this, partial);
  }
}
