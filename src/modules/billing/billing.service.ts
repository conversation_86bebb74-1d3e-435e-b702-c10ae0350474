import * as crypto from 'node:crypto';

import { BadRequestException, Injectable, Logger, ConflictException, UnauthorizedException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CreditEntity } from './entities/credit.entity';
import { UserService } from '../user/user.service';
import { CreditRepository } from './credit.repository';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { GetManyDtoOptions } from '../../common/abstract.repository';
import { Transactional } from 'typeorm-transactional';
import { v4 as uuidv4 } from 'uuid';
import { CreditLowEvent, NOTIFICATION_EVENTS } from '../mail/interfaces/notification-events.interface';
import { InvoiceService } from './invoice.service';
import { ApiConfigService } from 'src/shared/services/api-config.service';
import axios from 'axios';

@Injectable()
export class BillingService {
  private readonly logger = new Logger(BillingService.name);

  constructor(
    private readonly creditRepo: CreditRepository,
    private readonly userService: UserService,
    private readonly eventEmitter: EventEmitter2,
    private readonly apiConfig: ApiConfigService,
  ) { }

  async getMany<T = CreditEntity>(q: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
    const items = await this.creditRepo.getMany<T>(q, opt);

    return items;
  }

  /**
   * Gets or creates a credit record for the user.
   */
  @Transactional()
  async getOrCreateUserCredit(userId: string): Promise<CreditEntity> {
    const user = await this.userService.findById(userId);
    let credit = await this.creditRepo.findOne({ where: { userId: user.id } });

    if (!credit) {
      credit = this.creditRepo.create({
        userId: user.id,
        available: 0,
        used: 0,
      });

      try {
        await this.creditRepo.save(credit);
      } catch (e) {
        this.logger.warn(`Credit creation race for user ${user.id}`);
        credit = await this.creditRepo.findOrFail({ where: { userId: user.id } });
      }
    }

    return credit;
  }

  /**
   * Returns available (non-expired) credits for a user.
   */
  async getUserCredit(userId: string): Promise<number> {
    const credit = await this.getOrCreateUserCredit(userId);
    return credit.available - credit.used;
  }

  /**
   * Adds credit to a user's balance and optionally extend expiry.
   */
  @Transactional()
  async addCredit(userId: string, amount: number): Promise<CreditEntity> {
    if (amount <= 0) {
      throw new BadRequestException('Amount to add must be greater than zero.');
    }

    const credit = await this.getOrCreateUserCredit(userId);
    credit.available += amount;

    return await this.creditRepo.save(credit);
  }

  /**
   * Consumes credits for a user if they have enough and not expired.
   */
  @Transactional()
  async consumeCredit(userId: string, amount: number): Promise<CreditEntity> {
    if (amount <= 0) {
      throw new BadRequestException('Amount to consume must be greater than zero.');
    }

    const credit = await this.getOrCreateUserCredit(userId);

    if (credit.available - credit.used < amount) {
      throw new ConflictException(
        `Insufficient credits. Available: ${credit.available - credit.used}, required: ${amount}`,
      );
    }

    credit.used += amount;
    const updatedCredit = await this.creditRepo.save(credit);

    // Check if credit level warrants a low credit notification
    await this.checkAndEmitLowCreditEvent(updatedCredit);

    return updatedCredit;
  }

  /**
   * Returns whether the user has enough unexpired credit.
   */
  @Transactional()
  async hasSufficientCredit(userId: string, required: number): Promise<boolean> {
    if (required <= 0) return true;

    const credit = await this.getOrCreateUserCredit(userId);
    return credit.available - credit.used >= required;
  }

  /**
   * Resets user's credit regardless of expiry.
   */
  @Transactional()
  async resetCredit(userId: string): Promise<CreditEntity> {
    const credit = await this.getOrCreateUserCredit(userId);
    credit.available = 0;
    credit.used = 0;
    return await this.creditRepo.save(credit);
  }

  /**
   * Ensures sufficient and unexpired credit or throws.
   */

  async ensureSufficientCreditOrThrow(userId: string, required: number): Promise<void> {
    const credit = await this.getOrCreateUserCredit(userId);
    if (credit.available - credit.used < required) {
      throw new ConflictException('Not enough usable credits. Please recharge.');
    }
  }

  /**
   * Check credit level and emit low credit event if necessary
   */
  private async checkAndEmitLowCreditEvent(credit: CreditEntity): Promise<void> {
    try {
      const remainingCredits = credit.available - credit.used;
      const totalCredits = credit.available;

      // Skip if no credits or already at zero
      if (totalCredits <= 0 || remainingCredits <= 0) {
        return;
      }

      const percentageThreshold = Math.ceil(totalCredits * 0.1);
      const absoluteThreshold = 100;
      const threshold = Math.min(percentageThreshold, absoluteThreshold);

      if (remainingCredits <= threshold) {
        const user = await this.userService.findById(credit.userId);
        const usagePercentage = ((totalCredits - remainingCredits) / totalCredits) * 100;

        const event: CreditLowEvent = {
          timestamp: new Date(),
          eventId: uuidv4(),
          userId: credit.userId,
          user,
          credit,
          remainingCredits,
          totalCredits,
          usagePercentage,
          threshold,
        };

        this.eventEmitter.emit(NOTIFICATION_EVENTS.CREDIT_LOW, event);

        this.logger.log(
          `Low credit event emitted for user ${credit.userId}: ${remainingCredits}/${totalCredits} credits remaining`,
        );
      }
    } catch (error) {
      this.logger.error(`Failed to check credit level for user ${credit.userId}: ${error.message}`, error.stack);
    }
  }

  async generateTrySpeedCheckoutSession(creditAmount: number, userId: string, invoiceId: string) {
    const headers = {
      accept: 'application/json',
      'content-type': 'application/json',
      'speed-version': '2022-04-15',
      authorization: `Basic ${Buffer.from(`${this.apiConfig.tryspeedConfig.apiKey}:`).toString('base64')}`,
    };

    const data = {
      currency: "USD",
      amount: this.getUDSPerToken() * creditAmount,
      amount_paid_tolerance: 1,
      metadata: { creditAmount, userId, invoiceId },
      success_url: this.apiConfig.tryspeedConfig.successPaymentUrl,
      cancel_url: this.apiConfig.tryspeedConfig.failedPaymentUrl,
    };

    try {
      const response = await axios.post(
        `${this.apiConfig.tryspeedConfig.apiUrl}/checkout-sessions`, data, { headers });
      return response.data.url;
    } catch (error) {
      this.logger.error('Error generating checkout session:', error.response?.data || error.message);
      throw new Error('Could not generate checkout session');
    }
  }

  verifySignature(
    secret: string,
    signature: string,
    webhookId: string,
    timestamp: string,
    requestBody: string,
  ): boolean {
    try {
      signature = signature.slice(3);

      const cleanedSecret = secret.replace('wsec_', '');

      const tempSecret = Buffer.from(cleanedSecret, 'base64');

      const signedPayload = `${webhookId}.${timestamp}.${requestBody}`;

      const hmac = crypto.createHmac('sha256', tempSecret);
      hmac.update(signedPayload, 'utf8');
      const expectedSignature = hmac.digest('base64');

      return signature === expectedSignature;
    } catch (error) {
      console.error('Error during signature verification:', error);

      throw new UnauthorizedException('Invalid signature');
    }
  }

  tokenToCredit(tokenConsumed: number, toolCallCount: number) {
    let creditsToConsume = Math.ceil(tokenConsumed / this.getCreditPerToken());

    if (toolCallCount && toolCallCount > 0) {
      creditsToConsume += Math.floor(toolCallCount / 2);
    }
    creditsToConsume = Math.max(1, creditsToConsume);
    return creditsToConsume
  }

  getCreditPerToken() {
    return 1000
  }

  getUDSPerToken() {
    return 1
  }
}
