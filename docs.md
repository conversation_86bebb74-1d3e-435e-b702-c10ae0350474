# Rokovo internal document

### Users

Each user contains basic information such as `id`, `email`, `setting` (which contains more information such as `businessName`), avatar and more.

Users are created and authed using the Clerk service. 

Users have relation to other entities too, such as `session`, `knowledgeBase`, `transports`, `tools`, `prompts`, `credits`.

* `session`: When a new session starts, we make a new session and we point it to owner of agent.
* `knowledgeBase`: A list of information extracted form users documentations.
* `transports`: Some information such as access token for the Rokovo core to access a transport (like Telegram) to take the control and send messages on behalf of human agents.
* `tools`: A set of HTTP calls that we provide to agent due the process to use for answering questions.
* `prompts`: This is not controllable by user ATM. This is just a static field that stores user agent prompt. It let us to give a user multiple prompts, custom or predefined.
* `Credits`: Holds the information about how many credits user have. Credits need to be paid for. Using agent consume tokens.

### Tools

When a user defines a tool, in each turn of generating response we tell the agent that you can these tools as well. If agent needed those tools, it will call them to answer questions.

### Knowledge Base

User gives us a new document, that can be MD file, PDF, HTML or even plain text. We parse the document and turn it into a intermediate representation that looks like a plain text. We ML models to chunk these full document into smaller documents based on semantics. For example: `Only premium users can use feature X. To gain premium access they must go to pricing section and purchase a premium plan. Feature Y is free and anyone can use them to customize their profiles, and they don' need to take any specific actions.`. Will turn into 2 chunks:

A: `Only premium users can use feature X. To gain premium access they must go to pricing section and purchase a premium plan.`
B: `Feature Y is free and anyone can use them to customize their profiles, and they don' need to take any specific actions.`

> This is just an example.

We insert our documents into a database that later LLM can make query to retrieve information form it.

User can insert nee docs and/or remove previous docs.

### Session/Message/Feedback

When a new message is sent to the agent from any transport, the transport module will make  new session that contains a list of `message` that each message contains `content`, `role`, `feedback`.

Each new message from user will be marked as `user` role and we pass it to LLM to generate the response. The LLM response will be marked as `assistant` role. If user clicked on thumbs up/down button on each AI response, we record a positive or negative feedback for that specific message. Later we use those for fine-tuning.

### Transports

Each user can define a set of transports. Each transport have a field named `setting` and a field named `type`. Based on transport type, setting will be different. For example type `TELEGRAM` contains a token, while `API` contains an API key.

We use those information and credentials to listen for incoming messages and reply to them.

### Prompts

We define a prompt as `default_x_prompt` on our database. Where x is the purpose of prompt. For example:

`x` can be: `customer_support` (Currently available)
`x` can be: `sales_agent` (WIP for future)
`x` can be: `health_care_assistant` (Not planned yet)
`x` can be: `mentor` (Not planned yet)
`x` can be: `y_expert` (For example code expert for technical support or even development, Not planned yet)

Each new account will make a copy of default prompt called `customer_support_prompt`. Then when a new message is sent to a session owned by that user we use this prompt to ask the LLM for response. (Owner of session is not the user sending message to it, its the user that owns the agent in the Rokovo system. non Rokovo users called external user and we don't keep track of them that much for now).

By doing this, we can add more base prompts prefixing with `default` and let the user to choose between them. Or let to users to write custom prompts if they need.

> An optimal plan which may break the current thing is: turn users into `tenants`, and let each tenant have multiple users and multiple agents. Then admin/users with enough permissions can click on new agent button and in creation process they can select the prompt too. This is not yet planned. But it can enhance the dashboard and let us to make use of this dynamic prompt system much more.

> It also let us to give a new feature that users ask for paid custom prompts.

### Credits

Each user have a number called credits. Any part of system that consumes LLM tokens, it will cost credit and to gain more credit user have to pay. Current actions that cost credits:

1. Adding a new data to knowledge base costs credits based on document size since semantic chunking is processed by ML models and their consumption is based on tokens.

2. When an agent owned by user X answers user questions, it consumes tokens based LLM model. So it cost tokens.

We consider some amount of tokens together as 1 credit and when that amount of credit used, we subtract that from users account. 

Creating tools, transports or setting up business information will be free and they don't consume tokens.

> A not planned idea is to charge users for calling tools too (not defining them, calling). when a LLM calls a tool, it consume token which means it already costs for agent owner. But when LLM asked for the tool call, the execution of call happens on our servers and then we pass the result to LLM. In this case we may need to charge user for execution too, but its not a thing yet.

> Later we need to wrap tokens into plan as well to work with platforms like shopify too.
