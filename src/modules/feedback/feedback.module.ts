import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FeedbackEntity } from './entities/feedback.entity';
import { FeedbackRepository } from './feedback.repository';
import { FeedbackService } from './feedback.service';
import { MessagesModule } from '../messages/messages.module';
import { SessionsModule } from '../sessions/sessions.module';
import { UserModule } from '../user/user.module';
import { TransportModule } from '../transports/transport.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([FeedbackEntity]),
    MessagesModule,
    SessionsModule,
    UserModule,
    forwardRef(() => TransportModule),
  ],
  providers: [FeedbackRepository, FeedbackService],
  controllers: [],
  exports: [FeedbackService, FeedbackRepository],
})
export class FeedbackModule {}
