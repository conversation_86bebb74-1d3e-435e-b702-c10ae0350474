import { UUIDField, NumberField, DateField, EnumField } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { InvoiceDto } from './invoice.dto';
import { CreditEntity } from '../entities/credit.entity';

export class BillingDto extends AbstractDto {
  @UUIDField()
  userId: string;

  @NumberField()
  available: number;

  @NumberField()
  used: number;

  invoices?: InvoiceDto[];

  constructor(entity: CreditEntity) {
    super(entity);

    this.userId = entity.userId;
    this.available = entity.available;
    this.invoices = entity.invoices?.toDtos();
    this.used = entity.used;
  }
}
