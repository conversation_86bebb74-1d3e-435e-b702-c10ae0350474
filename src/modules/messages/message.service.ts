import { Injectable } from '@nestjs/common';
import { MessageRepository } from './message.repository';
import { MessageRole } from './enums/role.enum';
import { MessageEntity } from './entities/messages.entity';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { GetManyDtoOptions } from '../../common/abstract.repository';

@Injectable()
export class MessageService {
  constructor(private readonly repo: MessageRepository) { }

  async create(content: string, role: MessageRole, sessionId: string) {
    const m = this.repo.create({
      content,
      role,
      sessionId,
    });

    return this.repo.save(m);
  }

  async retrieveSessionHistory(sessionId: string) {
    const res = await this.repo.findAll({
      where: {
        sessionId,
      },
      order: {
        createdAt: 'ASC',
      },
      // select: ['content', 'role'],
    });

    return res;
  }

  async getMany<T = MessageEntity>(args: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
    const items = await this.repo.getMany<T>(args, opt);
    return items;
  }

  async getOne(id: string) {
    return this.repo.findOrFail({
      where: {
        id
      }
    });
  }
}
