import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { CreditHistoryEntity } from './entities/credit-history.entity';

@Injectable()
export class CreditHistoryRepository extends AbstractRepository<CreditHistoryEntity> {
  constructor(
    @InjectRepository(CreditHistoryEntity)
    repository: Repository<CreditHistoryEntity>,
  ) {
    super(repository);
  }
}

