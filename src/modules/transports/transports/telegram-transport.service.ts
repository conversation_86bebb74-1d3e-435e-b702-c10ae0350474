import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import axios from 'axios';
import { SessionService } from '../../../modules/sessions/session.service';
import { TransportRepository } from '../transport.repository';
import { TransportType } from '../enums/transport-type.enum';
import { TransportService } from '../transport.service';
import { UserService } from '../../../modules/user/user.service';
import { OnEvent } from '@nestjs/event-emitter';
import { FeedbackService } from '../../feedback/feedback.service';
import { FeedbackType } from '../../feedback/enums/feedback-type.enum';

@Injectable()
export class TelegramTransportService {
  private readonly logger = new Logger(TelegramTransportService.name);

  private readonly mainMenu = {
    reply_markup: {
      keyboard: [[{ text: '🆕 Start New Session' }, { text: '🛑 End Current Session' }]],
      resize_keyboard: true,
      one_time_keyboard: false,
    },
  };

  constructor(
    private readonly sessionService: SessionService,
    private readonly transportRepo: TransportRepository,
    private readonly transportService: TransportService,
    private readonly userService: UserService,
    private readonly feedbackService: FeedbackService,
  ) {}

  async handleUpdate(userId: string, update: any): Promise<void> {
    if (update.callback_query) {
      await this.handleCallbackQuery(userId, update.callback_query);
      return;
    }

    if (!update.message?.text) return;

    const chatId = update.message.chat.id.toString();
    const text = update.message.text.trim();

    const transport = await this.transportRepo.findOne({
      where: { type: TransportType.TELEGRAM },
    });
    if (!transport) throw new BadRequestException('Telegram transport not available');

    const { data: userTransports } = await this.transportService.getManyUserTransport(
      { filter: [`userId:eq:${userId}`, `transportId:eq:${transport.id}`] },
      {},
    );
    const botToken = userTransports[0]?.setting?.telegram?.token;
    if (!botToken) throw new BadRequestException('Telegram bot not set up');

    const user = await this.userService.findById(userId);
    let sessionId: string;

    try {
      if (text === '/start') {
        await this.send(
          botToken,
          chatId,
          `🤖 Welcome to ${user.setting.variables.businessName} support!\n\n` +
            `I'm ${user.setting.variables.agentName}, your AI assistant.\nHow can I help you today? Feel free to ask your question below.`,
          this.mainMenu,
        );
        return;
      }

      if (text === '🛑 End Current Session') {
        const active = await this.sessionService.findActiveSession(userId, transport.id, chatId);
        if (active) {
          await this.sessionService.closeSession(active.id);
          await this.send(
            botToken,
            chatId,
            `✅ Your session has been successfully *closed*.\n\nYou can start a new support session anytime from the menu below.`,
            this.mainMenu,
          );
        } else {
          await this.send(
            botToken,
            chatId,
            `⚠️ There is no active session to close.\n\nIf you need help, please start a new support session.`,
            this.mainMenu,
          );
        }
        return;
      }

      const existing = await this.sessionService.findActiveSession(userId, transport.id, chatId);
      if (text === '🆕 Start Support Session' || !existing) {
        const session = await this.sessionService.initSession(userId, chatId, transport.id);
        sessionId = session.id;
        await this.send(
          botToken,
          chatId,
          `🆕 A new support session has started!\n\nPlease send your message and I’ll assist you as quickly as possible.`,
          this.mainMenu,
        );

        return;
      } else {
        sessionId = existing.id;
      }

      this.sendChatAction(botToken, chatId, 'typing');

      const replies = await this.sessionService.continueSession(sessionId, text);
      const last = replies.at(-1);
      if (!last?.content) {
        this.logger.error(`Session ${sessionId}: no content returned`);
        throw new InternalServerErrorException('No reply from assistant');
      }

      const feedbackKeyboard = this.createFeedbackKeyboard(last.id);
      await this.send(botToken, chatId, last.content, feedbackKeyboard);
    } catch (err) {
      this.logger.error(`Error in Telegram handler: ${err.message}`, err.stack);
      await this.send(
        botToken,
        chatId,
        `🚨 *Oops!* Something went wrong on our end.\n\nPlease try again in a few moments. If the problem continues, contact us directly.`,
        this.mainMenu,
      );
    }
  }

  @OnEvent('session.closed')
  async handleClose(payload: { sessionId: string; externalUserId: string }) {
    const session = await this.sessionService.findOne(payload.sessionId);

    const { data } = await this.transportService.getManyUserTransport(
      { filter: [`userId:eq:${session.userId}`, `transportId:eq:${session.transportId}`] },
      {},
    );

    await new Promise((r) => setTimeout(r, 2000));

    if (!data[0]) {
      throw new NotFoundException('transport user not found');
    }

    if (!data[0].setting.telegram) return;

    const botToken = data[0].setting.telegram.token!;
    await this.send(
      botToken,
      payload.externalUserId,
      `🔒 Your session has been *closed by our support agent*.\n\nIf this was a mistake or you still need help, feel free to start a new session anytime.`,
      this.mainMenu,
    );
  }

  async closeSession(sessionId: string): Promise<void> {
    const ok = await this.sessionService.closeSession(sessionId);
    if (!ok) {
      this.logger.warn(`closeSession failed for ${sessionId}`);
      throw new NotFoundException(`Session ${sessionId} not found or already closed`);
    }
  }

  private async send(token: string, chatId: string, text: string, markup?: any) {
    const payload: any = { chat_id: chatId, text, reply_markup: markup?.reply_markup };
    payload.parse_mode = 'MarkdownV2';
    payload.text = this.escapeMarkdownV2(text);

    try {
      const resp = await axios.post(`https://api.telegram.org/bot${token}/sendMessage`, payload);
      return resp.data.result;
    } catch (err: any) {
      this.logger.error(`Send failed to chat ${chatId}: ${err.message}`);
      if (err.response?.data?.description?.includes("can't parse entities")) {
        this.logger.warn('MarkdownV2 parse error, retrying with HTML');
        payload.parse_mode = 'HTML';
        payload.text = this.escapeHTML(text);
        const resp2 = await axios.post(`https://api.telegram.org/bot${token}/sendMessage`, payload);
        return resp2.data.result;
      } else {
        throw new InternalServerErrorException('Failed to send message');
      }
    }
  }

  private async sendChatAction(
    token: string,
    chatId: string,
    action: 'typing' | 'upload_photo' | 'record_audio' = 'typing',
  ) {
    try {
      await axios.post(`https://api.telegram.org/bot${token}/sendChatAction`, {
        chat_id: chatId,
        action,
      });
    } catch (err: any) {
      this.logger.warn(`sendChatAction failed: ${err.response?.data?.description || err.message}`);
    }
  }

  private escapeMarkdownV2(text: string): string {
    return text.replace(/[_*[\]()~`>#+\-=|{}.!\\]/g, '\\$&');
  }

  private escapeHTML(text: string): string {
    return text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;');
  }

  private createFeedbackKeyboard(messageId: string) {
    return {
      reply_markup: {
        inline_keyboard: [
          [
            {
              text: '👍',
              callback_data: `f:p:${messageId}`,
            },
            {
              text: '👎',
              callback_data: `f:n:${messageId}`,
            },
          ],
        ],
      },
    };
  }

  private async handleCallbackQuery(userId: string, callbackQuery: any): Promise<void> {
    const { id: queryId, data: callbackData, message, from } = callbackQuery;
    const chatId = message.chat.id.toString();

    try {
      const [action, feedbackType, messageId] = callbackData.split(':');

      if (action !== 'f') {
        return;
      }

      const transport = await this.transportRepo.findOne({
        where: { type: TransportType.TELEGRAM },
      });
      if (!transport) {
        return;
      }

      const { data: userTransports } = await this.transportService.getManyUserTransport(
        { filter: [`userId:eq:${userId}`, `transportId:eq:${transport.id}`] },
        {},
      );
      const botToken = userTransports[0]?.setting?.telegram?.token;
      if (!botToken) {
        return;
      }

      const feedback = feedbackType === 'p' ? FeedbackType.POSITIVE : FeedbackType.NEGATIVE;

      try {
        await this.feedbackService.createFeedback(feedback, messageId);

        const emoji = feedback === FeedbackType.POSITIVE ? '👍' : '👎';
        await this.answerCallbackQuery(botToken, queryId, `${emoji} Thank you for your feedback!`);

        await this.editMessageReplyMarkup(botToken, chatId, message.message_id, {
          inline_keyboard: [
            [
              {
                text: `${emoji} Feedback submitted`,
                callback_data: 'feedback_submitted',
              },
            ],
          ],
        });
      } catch (error) {
        this.logger.error(`Feedback submission error: ${error.message}`);
        await this.answerCallbackQuery(botToken, queryId, '❌ Failed to submit feedback');
      }
    } catch (error) {
      this.logger.error(`Callback query error: ${error.message}`);
    }
  }

  private async answerCallbackQuery(token: string, queryId: string, text: string): Promise<void> {
    try {
      await axios.post(`https://api.telegram.org/bot${token}/answerCallbackQuery`, {
        callback_query_id: queryId,
        text,
        show_alert: false,
      });
    } catch (err: any) {
      this.logger.warn(`answerCallbackQuery failed: ${err.response?.data?.description || err.message}`);
    }
  }

  private async editMessageReplyMarkup(
    token: string,
    chatId: string,
    messageId: number,
    replyMarkup: any,
  ): Promise<void> {
    try {
      await axios.post(`https://api.telegram.org/bot${token}/editMessageReplyMarkup`, {
        chat_id: chatId,
        message_id: messageId,
        reply_markup: replyMarkup,
      });
    } catch (err: any) {
      this.logger.warn(`editMessageReplyMarkup failed: ${err.response?.data?.description || err.message}`);
    }
  }
}
