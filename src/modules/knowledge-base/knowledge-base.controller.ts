import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  NotFoundException,
  Param,
  Post,
  Query,
  UnauthorizedException,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { KnowledgeBaseService } from './knowledge-base.service';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { KnowledgeBaseEntity } from './entities/knowledge-base.entity';
import { GetManyDtoOptions } from '../../common/abstract.repository';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import path, { join } from 'path';
import { acceptableSource } from './enums/source-type.enum';
import { SpaceService } from '../../shared/services/space.service';
import { KnowledgeBaseStatus } from './enums/knowledge-base-status.enum';
import { AuthUser } from '../auth/decorators/auth-user.decorator';
import { User } from '@clerk/backend';
import { GuardUser } from '../../decorators/guard-user.decorator';
import { ApiConfigService } from '../../shared/services/api-config.service';
import { BillingService } from '../billing/billing.service';
import { RdeCallbackDto } from './dto/rde-callback.dto';
import { ApiBody, ApiConsumes, ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import { createLinkKnowledgeBaseDto } from './dto/create-link-knowledge-base.dto';
import { replaceGetManyFilter } from '../../common/utils';
import { CreatePlainTextKnowledgeBaseDto } from './dto/create-plain-text-knowledge-base.dto';
import { CreateQAKnowledgeBaseDto } from './dto/create-qa-knowledge-base.dto copy';
import { Public } from '../../decorators/public.decorator';

@Controller('knowledge-base')
@ApiTags('Knowledge Base')
export class KnowledgeBaseController {
  constructor(
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly spaceService: SpaceService,
    private readonly apiConfig: ApiConfigService,
    private readonly billingService: BillingService,
  ) {}

  @Post('file')
  @GuardUser()
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      limits: { fileSize: 10 * 1024 * 1024 },
      fileFilter: (_req, file, cb) => {
        const ext = path.extname(file.originalname).toUpperCase().replace('.', '');
        if (!Object.values(acceptableSource).includes(ext as acceptableSource)) {
          return cb(new BadRequestException('Invalid file type'), false);
        }
        cb(null, true);
      },
    }),
  )
  @ApiOperation({ summary: 'Upload a file as a knowledge base source' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Upload a supported file (e.g. .PDF, .TXT) to be processed as a knowledge base.',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
      required: ['file'],
    },
  })
  async create(@AuthUser() user: User, @UploadedFile() file: Express.Multer.File) {
    if (!file) throw new BadRequestException('File is required');
    return this.knowledgeBaseService.createFile(user.externalId!, file);
  }

  @Post('link')
  @GuardUser()
  @ApiOperation({ summary: 'Create a knowledge base from a link (URL)' })
  async createLink(@AuthUser() user: User, @Body() { link }: createLinkKnowledgeBaseDto) {
    return this.knowledgeBaseService.createLink(user.externalId!, link);
  }

  @Post('text')
  @GuardUser()
  @ApiOperation({ summary: 'Create a knowledge base from raw text (stored as Markdown)' })
  async createFromText(@AuthUser() user: User, @Body() body: CreatePlainTextKnowledgeBaseDto) {
    return this.knowledgeBaseService.createFromText(user.externalId!, body.content);
  }

  @Post('qa')
  @GuardUser()
  @ApiOperation({ summary: 'Create a knowledge base from Q&A (Markdown FAQ format)' })
  async createFromQA(@AuthUser() user: User, @Body() body: CreateQAKnowledgeBaseDto) {
    return this.knowledgeBaseService.createFromQA(user.externalId!, body.QaPairs);
  }

  @Get()
  @GuardUser()
  @ApiOperation({ summary: 'Get a list of knowledge bases' })
  async getMany(@AuthUser() user: User, @Query() query: GetManyDto<KnowledgeBaseEntity>) {
    query.filter = replaceGetManyFilter(query.filter, 'userId', `userId:eq:${user.externalId}`);
    return this.knowledgeBaseService.getMany(query, { toDto: true });
  }

  @Post('callback')
  @ApiOperation({ summary: 'Callback endpoint to update knowledge base status after processing' })
  @ApiHeader({
    name: 'api-key',
    required: true,
    description: 'RDE API key for validation',
  })
  @Public()
  async callback(@Body() body: RdeCallbackDto, @Headers('api-key') apiKey: string) {
    if (apiKey != this.apiConfig.rdeConfig.apiKey) {
      throw new UnauthorizedException('Invalid API key');
    }

    const { callbackId, isOk, consumedToken } = body;

    if (!callbackId) {
      throw new BadRequestException('Invalid callback data');
    }

    const { data } = await this.knowledgeBaseService.getMany(
      {
        filter: [`rdeCallbackId:eq:${callbackId}`],
        limit: 1,
        page: 1,
      },
      {},
    );

    const kb = data[0];

    if (!kb) {
      throw new BadRequestException('Knowledge base not found');
    }

    await this.knowledgeBaseService.update(kb.id, {
      status: isOk ? KnowledgeBaseStatus.FINISH : KnowledgeBaseStatus.FAILED,
    });

    const creditsToConsume = this.billingService.tokenToCredit(consumedToken, 0)
    await this.billingService.consumeCredit(kb.userId, creditsToConsume);
  }

  @Get('vectors')
  @GuardUser()
  @ApiOperation({ summary: 'Get vectors from a knowledge base (Qdrant)' })
  async getVectors(@AuthUser() user: User) {
    return this.knowledgeBaseService.getKnowledgeBase(user.externalId!);
  }

  @Get('vectors/search')
  @GuardUser()
  @ApiOperation({ summary: 'Find vectors from a knowledge base (Qdrant)' })
  async searchVectors(@AuthUser() user: User, @Query('q') q: string) {
    return this.knowledgeBaseService.findKnowledgeBaseFromVectorDB(user.externalId!, q);
  }

  @Delete('vectors/:vectorId')
  @GuardUser()
  @ApiOperation({ summary: 'Delete a vector from a knowledge base (Qdrant)' })
  async deleteVector(@AuthUser() user: User, @Param('vectorId') vectorId: string) {
    await this.knowledgeBaseService.deleteFromKnowledgeBase(user.externalId!, vectorId);
  }

  @Get('/download/:id')
  @GuardUser()
  @ApiOperation({ summary: 'Get download link of knowledge bases' })
  async getDownloadLink(@AuthUser() user: User, @Param('id') id: string) {
    const kb = await this.knowledgeBaseService.getOne(id);
    if (kb.userId != user.externalId!) {
      throw new NotFoundException('knowledge base not found');
    }

    if (!kb.s3Id) {
      throw new BadRequestException('knowledge base unable to download');
    }

    const link = await this.spaceService.getSignedUrl(kb.s3Id, 120);

    return link;
  }

  @Get(':id')
  @GuardUser()
  @ApiOperation({ summary: 'Get a single knowledge base by ID' })
  async getOne(@AuthUser() user: User, @Param('id') id: string) {
    const { data } = await this.knowledgeBaseService.getMany(
      { filter: [`userId:eq:${user.externalId}`, `id:eq:${id}`], page: 1, limit: 1, join: ['user'] },
      { toDto: true, joinables: ['user'] },
    );

    if (data.length == 0) {
      throw new NotFoundException('knowledge base not found');
    }

    return data[0];
  }
}
