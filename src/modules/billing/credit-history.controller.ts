import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { CreditHistoryService } from './credit-history.service';
import { GuardUser } from '../../decorators/guard-user.decorator';
import { AuthUser } from '../auth/decorators/auth-user.decorator';
import { User } from '@clerk/backend';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { ApiGetManyDto } from '../../decorators';
import { replaceGetManyFilter } from '../../common/utils';
import { CreditHistoryDto } from './dtos/credit-history.dto';
import { CreditHistoryEntity } from './entities/credit-history.entity';

@Controller('credit-history')
@ApiTags('Credit History')
export class CreditHistoryController {
  constructor(private readonly service: CreditHistoryService) {}

  @Get()
  @ApiOperation({ summary: 'List credit consumption history for current user' })
  @ApiGetManyDto({ type: CreditHistoryDto, description: 'Paginated list of credit history' })
  @GuardUser()
  async getMany(@AuthUser() user: User, @Query() q: GetManyDto<CreditHistoryEntity>) {
    q.filter = replaceGetManyFilter(q.filter, 'userId', `userId:eq:${user.externalId!}`);

    return this.service.getMany(q, {
      toDto: true,
      sortables: ['createdAt', 'amount', 'reason', 'sessionId'],
      filterables: ['userId', 'sessionId', 'reason', 'createdAt'],
      selectables: ['id', 'userId', 'sessionId', 'amount', 'reason', 'createdAt'],
    });
  }
}

