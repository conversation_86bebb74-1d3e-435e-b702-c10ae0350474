import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TransportEntity } from './entities/transport.entity';
import { TransportRepository } from './transport.repository';
import { TransportController } from './transport.controller';
import { TransportUserEntity } from './entities/transport-user.entity';
import { TransportService } from './transport.service';
import { UserModule } from '../user/user.module';
import { TransportUserRepository } from './transport-user.repository';
import { SessionsModule } from '../sessions/sessions.module';
import { PureTransportService } from './transports/pure-transport.service';
import { TransportPureController } from './transports/pure-transport.controller';
import { TransportTelegramController } from './transports/telegram-transport.controller';
import { TelegramTransportService } from './transports/telegram-transport.service';
import { HttpModule } from '@nestjs/axios';
import { AgentTestController } from './transports/test-agent.controller';
import { WidgetTransportService } from './transports/widget-transport.service';
import { WidgetTransportController } from './transports/widget-transport.controller';
import { FeedbackModule } from '../feedback/feedback.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TransportEntity, TransportUserEntity]),
    UserModule,
    SessionsModule,
    HttpModule,
    forwardRef(() => FeedbackModule),
  ],
  providers: [
    TransportRepository,
    TransportUserRepository,
    TransportService,
    PureTransportService,
    TelegramTransportService,
    WidgetTransportService
  ],
  controllers: [TransportController, TransportPureController, TransportTelegramController, AgentTestController, WidgetTransportController],
  exports: [TransportService, TransportRepository],
})
export class TransportModule {}
