import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CreditEntity } from './entities/credit.entity';
import { InvoiceEntity } from './entities/invoice.entity';
import { InvoiceService } from './invoice.service';
import { BillingService } from './billing.service';
import { InvoiceRepository } from './invoice.repository';
import { CreditRepository } from './credit.repository';
import { InvoiceController } from './invoice.controller';
import { BillingController } from './billing.controller';
import { UserModule } from '../user/user.module';
import { BillingAdminController } from './billing-admin.controller';
import { InvoiceAdminController } from './invoice-admin.controller';
import { CreditHistoryEntity } from './entities/credit-history.entity';
import { CreditHistoryRepository } from './credit-history.repository';
import { CreditHistoryService } from './credit-history.service';
import { CreditHistoryController } from './credit-history.controller';

@Module({
  imports: [
    UserModule,
    TypeOrmModule.forFeature([CreditEntity, InvoiceEntity, CreditHistoryEntity])
  ],
  providers: [InvoiceService, BillingService, InvoiceRepository, CreditRepository, CreditHistoryRepository, CreditHistoryService],
  controllers: [InvoiceController, BillingController, BillingAdminController, InvoiceAdminController, CreditHistoryController],
  exports: [BillingService, InvoiceService, CreditRepository, InvoiceRepository, CreditHistoryService, CreditHistoryRepository],
})
export class BillingModule {}
