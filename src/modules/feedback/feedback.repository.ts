import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { FeedbackEntity } from './entities/feedback.entity';

@Injectable()
export class FeedbackRepository extends AbstractRepository<FeedbackEntity> {
  constructor(
    @InjectRepository(FeedbackEntity)
    repository: Repository<FeedbackEntity>,
  ) {
    super(repository);
  }
}