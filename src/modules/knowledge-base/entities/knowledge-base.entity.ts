import { AbstractEntity } from '../../../common/abstract.entity';
import { Colum<PERSON>, <PERSON><PERSON><PERSON>, Join<PERSON>olum<PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { UserEntity } from '../../../modules/user/entities/user.entity';
import { KnowledgeBaseDto } from '../dto/Knowledge-base.dto';
import { KnowledgeBaseStatus } from '../enums/knowledge-base-status.enum';
import { KnowledgeBaseSourceType } from '../enums/source-type.enum';

@Entity('knowledge-base')
export class KnowledgeBaseEntity extends AbstractEntity<KnowledgeBaseDto> {
  dtoClass = KnowledgeBaseDto;

  @ManyToOne(() => UserEntity, (user) => user.knowledgeBase)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column()
  userId: string;

  @Column()
  name: string;

  @Column({ type: 'enum', enum: KnowledgeBaseSourceType })
  sourceType: KnowledgeBaseSourceType;

  @Column({ default: KnowledgeBaseStatus.PENDING })
  status: KnowledgeBaseStatus;

  @Column({ nullable: true })
  rdeCallbackId?: string;

  @Column({ nullable: true })
  s3Id?: string;

  constructor(item?: Partial<KnowledgeBaseEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<KnowledgeBaseEntity>): void {
    super.assign(item);

    this.userId = item.userId ?? this.userId;
    this.name = item.name ?? this.name;
    this.sourceType = item.sourceType ?? this.sourceType;
    this.rdeCallbackId = item.rdeCallbackId ?? this.rdeCallbackId;
    this.status = item.status ?? this.status;
    this.s3Id = item.s3Id ?? this.s3Id;
  }
}
