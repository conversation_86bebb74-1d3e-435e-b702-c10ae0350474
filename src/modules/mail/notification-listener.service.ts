import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { NotificationService } from './notification.service';

import {
  InvoicePaidEvent,
  CreditLowEvent,
  NOTIFICATION_EVENTS,
  InvoicePaidTemplateContext,
  LowCreditTemplateContext
} from './interfaces/notification-events.interface';

@Injectable()
export class NotificationListener {
  private readonly logger = new Logger(NotificationListener.name);

  constructor(
    private readonly notificationService: NotificationService,
  ) {}

  // @OnEvent(NOTIFICATION_EVENTS.INVOICE_PAID)
  // async handleInvoicePaidEvent(payload: InvoicePaidEvent): Promise<void> {
  //   try {
  //     this.logger.log(`Handling invoice paid event for user ${payload.user.id}`);

  //     // Calculate new balance (this would ideally come from the credit record)
  //     const newBalance = payload.creditsPurchased; // Simplified - in real scenario, get actual balance

  //     const context: InvoicePaidTemplateContext = {
  //       user_name: payload.user.name,
  //       user_email: payload.user.email,
  //       invoice_number: payload.invoice.paymentProviderId.toString(),
  //       payment_date: payload.invoice.paidAt?.toLocaleDateString() || new Date().toLocaleDateString(),
  //       credits_purchased: payload.creditsPurchased.toString(),
  //       payment_method: payload.invoice.paymentMethod || 'Credit Card',
  //       total_amount: payload.invoice.amount.toString(),
  //       new_balance: newBalance.toString(),
  //       dashboard_url: emailConfig.accountUrl,
  //       download_invoice_url: `${emailConfig.accountUrl}/invoices/${payload.invoice.id}`,
  //       support_url: `mailto:${emailConfig.supportEmail}`,
  //       current_year: new Date().getFullYear().toString(),
  //     };

  //     await this.notificationService.sendNotificationEmail(
  //       payload.user.email,
  //       'Payment Confirmation - Credits Added to Your Account',
  //       'invoice-paid',
  //       context
  //     );

  //     this.logger.log(`Invoice paid notification sent successfully to ${payload.user.email}`);
  //   } catch (error) {
  //     this.logger.error(
  //       `Failed to send invoice paid notification to ${payload.user.email}: ${error.message}`,
  //       error.stack
  //     );
  //   }
  // }

  @OnEvent(NOTIFICATION_EVENTS.CREDIT_LOW)
  async handleCreditLowEvent(payload: CreditLowEvent): Promise<void> {
    try {
      this.logger.log(`Handling low credit event for user ${payload.userId}`);

      // Calculate estimated days remaining based on recent usage
      const usedCredits = payload.totalCredits - payload.remainingCredits;

      const context: LowCreditTemplateContext = {
        user_name: payload.user.name,
        user_email: payload.user.email,
        remaining_credits: payload.remainingCredits.toString(),
        monthly_usage: usedCredits.toString(),
        support_url: `mailto:<EMAIL>`,
        current_year: new Date().getFullYear().toString(),
      };

      await this.notificationService.sendNotificationEmail(
        payload.user.email,
        'Low Credit Warning - Rokovo',
        'low-credit',
        context
      );

      this.logger.log(`Low credit notification sent successfully to ${payload.user.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send low credit notification to ${payload.user.email}: ${error.message}`,
        error.stack
      );
    }
  }
}
