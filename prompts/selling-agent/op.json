{"name": "AI Sales Agent", "description": "A Sale agent to help shoppers", "promptVersion": "1.0.0", "opVersion": "v1", "models": ["openai/gpt-4o"], "source": {"prompt": "prompt.txt", "experimentDoc": "doc.md"}, "sampling": {"temp": 0.7, "topK": 50, "topP": 0.9}, "outputControl": {"maxTokens": 1000, "frequencyPenalty": 0.8, "presencePenalty": 0.5, "repetitionPenalty": 1.1, "stopSeq": "", "structure": "plain"}, "variables": {"shopType": {"type": "string", "description": "Type of business that agent tries to provide support for, like: Crypto Exchange, Data center and ...", "default": ""}, "shopName": {"type": "string", "description": "Name of business that agent tries to provide support for, like: Dezh Technologies, Google, Open AI, ...", "default": ""}, "agentName": {"type": "string", "description": "Name of the support agent, like: <PERSON>, <PERSON>yBot, ...", "default": ""}, "businessDescription": {"type": "string", "description": "A  description of business like: Binance is a leading crypto exchange that supports +1000 crypto currencies.", "default": ""}, "tools": {"type": {"name": {"description": "Tool details"}}, "description": "A list of available tools that the agent can use to answer user questions."}}}