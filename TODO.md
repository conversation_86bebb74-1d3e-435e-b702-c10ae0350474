# Possible improvements for future updates

* [ ] Sampling options controlled by user (maybe abstracted)
* [ ] Bot language(s) + document language in user configs (+ support AI translation for RAG documents)
* [ ] Callback on pure API when we emit: `session.closed`.
* [ ] Human In The Loop calls
* [ ] In app notifications
* [ ] Using multiple LLMs selected by user
* [ ] Multiple agents for each account
* [ ] Negative credits to prevent service stops randomly
* [ ] Add tool calls log to message history
* [ ] Different `session.closed` event types: `session.closed.agent`, `session.closed.system`, `session.closed.user`, `session.closed.admin`
* [ ] Better onboarding system
* [ ] Specific Tags for product suggestion in selling agents
* [ ] Delete account option

# Context

* [ ] Vibe initialization dashboard
* [ ] Controllable character like friendly, formal, energetic and ...
* [ ] Close a session to prevent high token usage
* [ ] Human readable errors for tools provided to AI
* [ ] Request customized/dynamic prompt.
* [ ] Optional markdown/HTML and ..., formatting.
* [ ] Truncating prompt to reduce usage
* [ ] Note taking after resolving each session, about what happened on that session
* [ ] A tool to retrieve past sessions notes
* [ ] Adding an overall overview to each session about past sessions of a user
* [ ] User info in model context: their first/last name, preferred language(?), Time zone, business specific data (their subscription status and ...).
* [ ] Detailed metrics and information for admin dashboard.
* [ ] Rokovo Vision and Audition

# Transports

* [ ] WhatsApp transport.
* [ ] Facebook Messenger transport.
* [ ] Discord transport.
* [ ] VoIP/Voice integration
* [ ] Instagram integration.
* [ ] Live Chat integration.
* [ ] Github issues transport.
* [ ] Slack integration.
* [ ] Weechat integration.
* [ ] Gitlab integration.
* [ ] Telegram bot support in groups.
* [ ] Email integration.
* [ ] SMS integration.

* [ ] Configurable setup for telegram bot: dynamic first message + jinja variables.
* [ ] Widget Auth
* [ ] Widget HITL
* [ ] Widget voice
* [ ] Widget embedding in Android/IOS apps


# Tools

* [ ] Shopify integration.
* [ ] WIX integration.
* [ ] Google calendar integration.
* [ ] Calendly integration.
* [ ] Whatsapp integration.
* [ ] Zendesk integration.
* [ ] Intercom integration.
* [ ] Wordpress integration.
